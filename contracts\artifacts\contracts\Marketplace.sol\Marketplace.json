{"_format": "hh-sol-artifact-1", "contractName": "Marketplace", "sourceName": "contracts/Marketplace.sol", "abi": [{"inputs": [{"internalType": "address", "name": "owner_", "type": "address"}, {"internalType": "address", "name": "token_", "type": "address"}, {"internalType": "address", "name": "feeRecipient_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "Cancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "projectId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "pricePerTokenWei", "type": "uint256"}], "name": "Listed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalPaid", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "feePaid", "type": "uint256"}], "name": "Purchased", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "buy", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "cancel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "feeBps", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeRecipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "projectId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "pricePerTokenWei", "type": "uint256"}], "name": "list", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "listings", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "projectId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "pricePerTokenWei", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextListingId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "newFeeBps", "type": "uint16"}, {"internalType": "address", "name": "newRecipient", "type": "address"}], "name": "setFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}