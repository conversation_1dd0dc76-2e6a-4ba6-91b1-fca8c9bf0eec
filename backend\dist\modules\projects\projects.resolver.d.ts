import { ProjectsService } from './projects.service';
declare class Project {
    id: string;
    name: string;
    geojson: string;
    ipfsHash: string | null;
    verified: boolean;
}
export declare class ProjectsResolver {
    private readonly svc;
    constructor(svc: ProjectsService);
    projects(): Promise<Project[]>;
    registerProject(name: string, geojson: string, ipfsHash: string): Promise<Project>;
}
export {};
