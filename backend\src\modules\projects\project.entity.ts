import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'projects' })
export class ProjectEntity {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'text' })
  name!: string;

  @Column({ type: 'text' })
  geojson!: string; // polygon GeoJSON string

  @Column({ type: 'text', nullable: true })
  ipfsHash!: string | null;

  @Column({ type: 'boolean', default: false })
  verified!: boolean;
}
