# Developer Quickstart

This repo scaffolds a Blue-Carbon Blockchain MRV & Carbon-Credit Registry prototype. It is designed to run fully on free tiers and local services.

## Prerequisites
- Node.js 18+
- Git
- Docker Desktop (for local Postgres+PostGIS and IPFS fallback)
- Optional: web3.storage API token (free-tier)
- Polygon Mumbai wallet with test MATIC

## Layout
- `contracts/` — Solidity (Hardhat)
- `backend/` — NestJS/Express GraphQL + REST
- `web/` — Next.js PWA (Tailwind)
- `mobile/` — Expo React Native
- `infra/` — CI/CD, Terraform
- `scripts/` — one-click scripts

## One-liners
- Install deps: `npm i`
- Dev (placeholder): `npm run dev`
- Test (placeholder): `npm run test`
- Deploy (placeholder): `npm run deploy`

These will be replaced by full implementations after code generation.

## Local Dev (after code generation)
1. Copy envs: `cp backend/.env.example backend/.env`
2. Start services: `docker compose -f infra/docker-compose.local.yml up -d`

If you don't want to run Postgres locally, the backend will automatically fall back to an in-memory SQLite database when `DATABASE_URL` is not set — useful for quick local testing.
3. Migrate + seed: `npm run backend:migrate && npm run backend:seed`
4. Start all apps: `npm run dev`

## Happy Path
Onboard project → upload MRV → NDVI computed → IPFS pin → on-chain verify → mint → list → buy/retire → receipt.

See `README.md` for details.
