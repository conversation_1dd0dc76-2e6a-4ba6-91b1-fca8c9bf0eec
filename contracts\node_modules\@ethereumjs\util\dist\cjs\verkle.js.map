{"version": 3, "file": "verkle.js", "sourceRoot": "", "sources": ["../../src/verkle.ts"], "names": [], "mappings": ";;;AAAA,yCASmB;AA2BnB;;;;;;;GAOG;AACH,SAAgB,aAAa,CAC3B,GAAiB,EACjB,OAAgB,EAChB,YAA6B,CAAC;IAE9B,MAAM,SAAS,GAAG,IAAA,wBAAa,EAAC,OAAO,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC,CAAA;IAEtD,IAAI,cAA0B,CAAA;IAC9B,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QACjC,cAAc,GAAG,IAAA,yBAAc,EAAC,IAAA,uBAAY,EAAC,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAA;KAC3E;SAAM;QACL,cAAc,GAAG,IAAA,yBAAc,EAAC,IAAA,wBAAa,EAAC,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;KACzF;IAED,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAE1E,OAAO,QAAQ,CAAA;AACjB,CAAC;AAjBD,sCAiBC;AAED;;;;;;GAMG;AACH,SAAgB,iBAAiB,CAC/B,GAAiB,EACjB,YAAwB,EACxB,gBAAwC;IAExC,OAAO,GAAG,CAAC,8BAA8B,CACvC,IAAA,qBAAU,EAAC,YAAY,CAAC,EACxB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CACjC,CAAA;AACH,CAAC;AATD,8CASC;AA2CD,IAAY,cAMX;AAND,WAAY,cAAc;IACxB,yDAAW,CAAA;IACX,yDAAW,CAAA;IACX,qDAAS,CAAA;IACT,2DAAY,CAAA;IACZ,2DAAY,CAAA;AACd,CAAC,EANW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAMzB;AAEY,QAAA,uBAAuB,GAAG,IAAA,qBAAU,EAAC,cAAc,CAAC,OAAO,CAAC,CAAA;AAC5D,QAAA,uBAAuB,GAAG,IAAA,qBAAU,EAAC,cAAc,CAAC,OAAO,CAAC,CAAA;AAC5D,QAAA,qBAAqB,GAAG,IAAA,qBAAU,EAAC,cAAc,CAAC,KAAK,CAAC,CAAA;AACxD,QAAA,yBAAyB,GAAG,IAAA,qBAAU,EAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;AAC/D,QAAA,yBAAyB,GAAG,IAAA,qBAAU,EAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;AAE/D,QAAA,4BAA4B,GAAG,EAAE,CAAA;AACjC,QAAA,kBAAkB,GAAG,GAAG,CAAA;AACxB,QAAA,iBAAiB,GAAG,GAAG,CAAA;AACvB,QAAA,0BAA0B,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,CAAA;AAEnE;;;;;;GAMG;AAEI,MAAM,YAAY,GAAG,CAAC,IAAgB,EAAE,IAAiC,EAAE,EAAE;IAClF,QAAQ,IAAI,EAAE;QACZ,KAAK,cAAc,CAAC,OAAO;YACzB,OAAO,IAAA,sBAAW,EAAC,IAAI,EAAE,+BAAuB,CAAC,CAAA;QACnD,KAAK,cAAc,CAAC,OAAO;YACzB,OAAO,IAAA,sBAAW,EAAC,IAAI,EAAE,+BAAuB,CAAC,CAAA;QACnD,KAAK,cAAc,CAAC,KAAK;YACvB,OAAO,IAAA,sBAAW,EAAC,IAAI,EAAE,6BAAqB,CAAC,CAAA;QACjD,KAAK,cAAc,CAAC,QAAQ;YAC1B,OAAO,IAAA,sBAAW,EAAC,IAAI,EAAE,iCAAyB,CAAC,CAAA;QACrD,KAAK,cAAc,CAAC,QAAQ;YAC1B,OAAO,IAAA,sBAAW,EAAC,IAAI,EAAE,iCAAyB,CAAC,CAAA;QACrD;YACE,OAAO,IAAA,sBAAW,EAAC,IAAI,EAAE,IAAI,CAAC,CAAA;KACjC;AACH,CAAC,CAAA;AAfY,QAAA,YAAY,gBAexB;AAED,SAAgB,kCAAkC,CAAC,UAAkB;IAInE,IAAI,QAAgB,CAAA;IACpB,IAAI,UAAU,GAAG,0BAAkB,GAAG,oCAA4B,EAAE;QAClE,QAAQ,GAAG,MAAM,CAAC,oCAA4B,CAAC,GAAG,UAAU,CAAA;KAC7D;SAAM;QACL,QAAQ,GAAG,kCAA0B,GAAG,UAAU,CAAA;KACnD;IAED,MAAM,SAAS,GAAG,QAAQ,GAAG,MAAM,CAAC,yBAAiB,CAAC,CAAA;IACtD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,yBAAiB,CAAC,CAAC,CAAA;IAE7D,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAA;AAChC,CAAC;AAfD,gFAeC;AAED,SAAgB,gCAAgC,CAAC,OAAe;IAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,0BAAkB,GAAG,OAAO,CAAC,GAAG,yBAAiB,CAAC,CAAA;IAChF,MAAM,QAAQ,GAAG,CAAC,0BAAkB,GAAG,OAAO,CAAC,GAAG,yBAAiB,CAAA;IACnE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAA;AAChC,CAAC;AAJD,4EAIC;AAEM,MAAM,4BAA4B,GAAG,KAAK,EAC/C,OAAgB,EAChB,OAAe,EACf,YAA0B,EAC1B,EAAE;IACF,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,gCAAgC,CAAC,OAAO,CAAC,CAAA;IACzE,OAAO,IAAA,sBAAW,EAAC,aAAa,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,IAAA,kBAAO,EAAC,QAAQ,CAAC,CAAC,CAAA;AACxF,CAAC,CAAA;AAPY,QAAA,4BAA4B,gCAOxC;AAEM,MAAM,YAAY,GAAG,CAAC,IAAgB,EAAE,EAAE;IAC/C,mCAAmC;IACnC,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE;QAC1B,MAAM,aAAa,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAA;QAC7C,IAAI,GAAG,IAAA,yBAAc,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,CAAA;KACzD;IAED,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAA;AACpC,CAAC,CAAA;AARY,QAAA,YAAY,gBAQxB;AAEM,MAAM,8BAA8B,GAAG,KAAK,EACjD,OAAgB,EAChB,UAAkB,EAClB,YAA0B,EAC1B,EAAE;IACF,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,kCAAkC,CAAC,UAAU,CAAC,CAAA;IAE9E,OAAO,IAAA,sBAAW,EAAC,aAAa,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,IAAA,kBAAO,EAAC,QAAQ,CAAC,CAAC,CAAA;AACxF,CAAC,CAAA;AARY,QAAA,8BAA8B,kCAQ1C"}