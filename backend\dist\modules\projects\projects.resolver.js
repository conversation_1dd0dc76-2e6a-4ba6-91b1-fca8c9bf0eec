"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectsResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const projects_service_1 = require("./projects.service");
let Project = class Project {
};
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], Project.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Project.prototype, "name", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], Project.prototype, "geojson", void 0);
__decorate([
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], Project.prototype, "ipfsHash", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Boolean)
], Project.prototype, "verified", void 0);
Project = __decorate([
    (0, graphql_1.ObjectType)()
], Project);
let ProjectsResolver = class ProjectsResolver {
    constructor(svc) {
        this.svc = svc;
    }
    async projects() {
        return (await this.svc.list());
    }
    async registerProject(name, geojson, ipfsHash) {
        return (await this.svc.register({ name, geojson, ipfsHash }));
    }
};
exports.ProjectsResolver = ProjectsResolver;
__decorate([
    (0, graphql_1.Query)(() => [Project]),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ProjectsResolver.prototype, "projects", null);
__decorate([
    (0, graphql_1.Mutation)(() => Project),
    __param(0, (0, graphql_1.Args)('name')),
    __param(1, (0, graphql_1.Args)('geojson')),
    __param(2, (0, graphql_1.Args)('ipfsHash')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], ProjectsResolver.prototype, "registerProject", null);
exports.ProjectsResolver = ProjectsResolver = __decorate([
    (0, graphql_1.Resolver)(() => Project),
    __metadata("design:paramtypes", [projects_service_1.ProjectsService])
], ProjectsResolver);
//# sourceMappingURL=projects.resolver.js.map