// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";

contract Marketplace is Ownable {
    struct Listing {
        uint256 id;
        address seller;
        uint256 projectId;
        uint256 amount; // token amount (18 decimals)
        uint256 pricePerTokenWei; // priced in native token wei per 1 token (1e18 units)
        bool active;
    }

    IERC20 public token;
    uint256 public nextListingId = 1;
    mapping(uint256 => Listing) public listings;

    uint16 public feeBps = 50; // 0.5%
    address public feeRecipient;

    event Listed(uint256 indexed id, address indexed seller, uint256 projectId, uint256 amount, uint256 pricePerTokenWei);
    event Purchased(uint256 indexed id, address indexed buyer, uint256 amount, uint256 totalPaid, uint256 feePaid);
    event Cancelled(uint256 indexed id);

    constructor(address owner_, address token_, address feeRecipient_) Ownable(owner_) {
        token = IERC20(token_);
        feeRecipient = feeRecipient_;
    }

    function setFee(uint16 newFeeBps, address newRecipient) external onlyOwner {
        require(newFeeBps <= 1000, "fee too high");
        feeBps = newFeeBps;
        feeRecipient = newRecipient;
    }

    function list(uint256 projectId, uint256 amount, uint256 pricePerTokenWei) external returns (uint256) {
        require(amount > 0 && pricePerTokenWei > 0, "bad params");
        uint256 id = nextListingId++;
        listings[id] = Listing({ id: id, seller: msg.sender, projectId: projectId, amount: amount, pricePerTokenWei: pricePerTokenWei, active: true });
        emit Listed(id, msg.sender, projectId, amount, pricePerTokenWei);
        return id;
    }

    function cancel(uint256 id) external {
        Listing storage l = listings[id];
        require(l.active, "inactive");
        require(l.seller == msg.sender || msg.sender == owner(), "not allowed");
        l.active = false;
        emit Cancelled(id);
    }

    function buy(uint256 id, uint256 amount) external payable {
        Listing storage l = listings[id];
        require(l.active, "inactive");
        require(amount > 0 && amount <= l.amount, "bad amount");
        // Adjust for 18-decimal token amounts: pricePerTokenWei is per whole token (1e18)
        uint256 totalPrice = (amount * l.pricePerTokenWei) / 1e18;
        require(msg.value >= totalPrice, "insufficient payment");

        // fee
        uint256 fee = (totalPrice * feeBps) / 10000;
        uint256 payout = totalPrice - fee;

        // transfer tokens from seller to buyer
        require(token.transferFrom(l.seller, msg.sender, amount), "transferFrom failed");

        // pay seller and fee recipient
        (bool ok1, ) = payable(l.seller).call{value: payout}("");
        (bool ok2, ) = payable(feeRecipient).call{value: fee}("");
        require(ok1 && ok2, "payout failed");

        l.amount -= amount;
        if (l.amount == 0) {
            l.active = false;
        }
        emit Purchased(id, msg.sender, amount, totalPrice, fee);

        // refund dust
        if (msg.value > totalPrice) {
            (bool ok3, ) = payable(msg.sender).call{value: msg.value - totalPrice}("");
            require(ok3, "refund failed");
        }
    }
}
