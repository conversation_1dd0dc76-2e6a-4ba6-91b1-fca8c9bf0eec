"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const dotenv = require("dotenv");
globalThis.crypto = globalThis.crypto || require('crypto').webcrypto;
const core_1 = require("@nestjs/core");
const app_module_1 = require("./modules/app.module");
const common_1 = require("@nestjs/common");
const express_1 = require("express");
dotenv.config();
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.useGlobalPipes(new common_1.ValidationPipe({ whitelist: true }));
    app.enableCors();
    app.use(express_1.default.json({ limit: '10mb' }));
    await app.listen(process.env.PORT ? Number(process.env.PORT) : 4000);
}
bootstrap();
//# sourceMappingURL=main.js.map