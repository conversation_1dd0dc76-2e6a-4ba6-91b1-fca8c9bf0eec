"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const dotenv = require("dotenv");
console.log('--- bootstrap start ---');
console.log('cwd:', process.cwd());
console.log('node version:', process.version);
console.log('PORT env:', process.env.PORT);
console.log('WEB3_STORAGE_TOKEN present ->', !!process.env.WEB3_STORAGE_TOKEN);
console.log('globalThis.crypto exists ->', typeof globalThis.crypto !== 'undefined');
try {
    globalThis.crypto = globalThis.crypto || require('crypto').webcrypto;
}
catch (e) {
    console.error('crypto polyfill failed:', e && e.stack ? e.stack : e);
}
dotenv.config();
require('./main');
//# sourceMappingURL=bootstrap.js.map