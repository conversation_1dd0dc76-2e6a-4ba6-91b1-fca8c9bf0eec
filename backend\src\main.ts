import 'reflect-metadata';
import * as dotenv from 'dotenv';
// Polyfill Web Crypto for libraries expecting globalThis.crypto in Node <19
// eslint-disable-next-line @typescript-eslint/no-var-requires
(globalThis as any).crypto = (globalThis as any).crypto || require('crypto').webcrypto;
import { NestFactory } from '@nestjs/core';
import { AppModule } from './modules/app.module';
import { ValidationPipe } from '@nestjs/common';
import express from 'express';

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
  app.enableCors();
  app.use(express.json({ limit: '10mb' }));

  await app.listen(process.env.PORT ? Number(process.env.PORT) : 4000);
}

bootstrap();
