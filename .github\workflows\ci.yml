name: ci
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
jobs:
  build_test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Install root
        run: npm i
      - name: Contracts install and test
        run: |
          cd contracts
          npm i
          npx hardhat compile
          npx hardhat test
      - name: Backend install and build
        run: |
          cd backend
          npm i
          npm run build
      - name: Web install and build
        run: |
          cd web
          npm i
          npm run build
