{"_format": "hh-sol-artifact-1", "contractName": "ICarbonProjectRegistryLike", "sourceName": "contracts/BlueCarbonToken.sol", "abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isVerifier", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}