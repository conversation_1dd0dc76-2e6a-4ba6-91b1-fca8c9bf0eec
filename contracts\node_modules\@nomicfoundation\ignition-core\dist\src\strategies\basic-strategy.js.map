{"version": 3, "file": "basic-strategy.js", "sourceRoot": "", "sources": ["../../../src/strategies/basic-strategy.ts"], "names": [], "mappings": ";;;AACA,iGAQ0D;AAE1D,mFAAmF;AAOnF,uFAOwD;AACxD,yFAAyF;AACzF,6DAAuE;AAEvE;;;;;;GAMG;AACH,MAAa,aAAa;IACR,IAAI,GAAW,OAAO,CAAC;IACvB,MAAM,CAA6B;IAE3C,iBAAiB,CAA+B;IAExD;QACE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,IAAI,CACf,gBAAkC,EAClC,cAA6B;QAE7B,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,CAAC,iBAAiB,CAC7B,cAAwC;QAExC,IAAA,oCAAuB,EACrB,IAAI,CAAC,iBAAiB,KAAK,SAAS,EACpC,YAAY,IAAI,CAAC,IAAI,kBAAkB,CACxC,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CACxD,cAAc,CAAC,UAAU,CAC1B,CAAC;QAEF,MAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,IAAA,6DAAgC,EACjE,cAAc,CAAC,EAAE,EACjB;YACE,EAAE,EAAE,CAAC;YACL,IAAI,EAAE,4CAAsB,CAAC,mBAAmB;YAChD,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,IAAA,yDAA4B,EAChC,QAAQ,EACR,cAAc,CAAC,eAAe,EAC9B,cAAc,CAAC,SAAS,CACzB;YACD,KAAK,EAAE,cAAc,CAAC,KAAK;SAC5B,EACD,SAAS,EACT,CAAC,UAAU,EAAE,EAAE,CAAC,IAAA,sDAAyB,EAAC,QAAQ,EAAE,UAAU,CAAC,CAChE,CAAC;QAEF,IACE,mBAAmB,CAAC,IAAI;YACxB,mDAA8B,CAAC,sBAAsB,EACrD;YACA,OAAO,mBAAmB,CAAC;SAC5B;QAED,MAAM,EAAE,GAAG,mBAAmB,CAAC,WAAW,CAAC;QAC3C,MAAM,eAAe,GAAG,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC;QAEnD,IAAI,eAAe,KAAK,SAAS,EAAE;YACjC,OAAO;gBACL,IAAI,EAAE,sCAAmB,CAAC,cAAc;gBACxC,KAAK,EAAE,eAAe,EAAE,CAAC,IAAI,4CAA4C;aAC1E,CAAC;SACH;QAED,OAAO;YACL,IAAI,EAAE,sCAAmB,CAAC,OAAO;YACjC,OAAO,EAAE,eAAe;SACzB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,CAAC,WAAW,CACvB,cAAkC;QAElC,IAAA,oCAAuB,EACrB,IAAI,CAAC,iBAAiB,KAAK,SAAS,EACpC,YAAY,IAAI,CAAC,IAAI,kBAAkB,CACxC,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CACxD,cAAc,CAAC,UAAU,CAC1B,CAAC;QAEF,MAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,IAAA,6DAAgC,EACjE,cAAc,CAAC,EAAE,EACjB;YACE,EAAE,EAAE,CAAC;YACL,IAAI,EAAE,4CAAsB,CAAC,mBAAmB;YAChD,EAAE,EAAE,cAAc,CAAC,eAAe;YAClC,IAAI,EAAE,IAAA,uDAA0B,EAC9B,QAAQ,EACR,cAAc,CAAC,YAAY,EAC3B,cAAc,CAAC,IAAI,CACpB;YACD,KAAK,EAAE,cAAc,CAAC,KAAK;SAC5B,EAED,CAAC,UAAU,EAAE,EAAE,CACb,IAAA,6DAAgC,EAC9B,QAAQ,EACR,cAAc,CAAC,YAAY,EAC3B,UAAU,CACX,EACH,CAAC,UAAU,EAAE,EAAE,CAAC,IAAA,sDAAyB,EAAC,QAAQ,EAAE,UAAU,CAAC,CAChE,CAAC;QAEF,IACE,mBAAmB,CAAC,IAAI;YACxB,mDAA8B,CAAC,sBAAsB,EACrD;YACA,OAAO,mBAAmB,CAAC;SAC5B;QAED,OAAO;YACL,IAAI,EAAE,sCAAmB,CAAC,OAAO;SAClC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,CAAC,eAAe,CAC3B,cAAsC;QAEtC,MAAM,mBAAmB,GAAG,KAAK,CAAC,CAAC,IAAA,6DAAgC,EACjE,cAAc,CAAC,EAAE,EACjB;YACE,EAAE,EAAE,CAAC;YACL,IAAI,EAAE,4CAAsB,CAAC,mBAAmB;YAChD,EAAE,EAAE,cAAc,CAAC,EAAE;YACrB,IAAI,EAAE,cAAc,CAAC,IAAI;YACzB,KAAK,EAAE,cAAc,CAAC,KAAK;SAC5B,CACF,CAAC;QAEF,IACE,mBAAmB,CAAC,IAAI;YACxB,mDAA8B,CAAC,sBAAsB,EACrD;YACA,OAAO,mBAAmB,CAAC;SAC5B;QAED,OAAO;YACL,IAAI,EAAE,sCAAmB,CAAC,OAAO;SAClC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,CAAC,iBAAiB,CAC7B,cAAwC;QAExC,IAAA,oCAAuB,EACrB,IAAI,CAAC,iBAAiB,KAAK,SAAS,EACpC,YAAY,IAAI,CAAC,IAAI,kBAAkB,CACxC,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CACxD,cAAc,CAAC,UAAU,CAC1B,CAAC;QAEF,MAAM,oBAAoB,GAAG,KAAK,CAAC,CAAC,IAAA,qDAAwB,EAC1D;YACE,EAAE,EAAE,CAAC;YACL,IAAI,EAAE,4CAAsB,CAAC,WAAW;YACxC,EAAE,EAAE,cAAc,CAAC,eAAe;YAClC,IAAI,EAAE,cAAc,CAAC,IAAI;YACzB,IAAI,EAAE,IAAA,uDAA0B,EAC9B,QAAQ,EACR,cAAc,CAAC,YAAY,EAC3B,cAAc,CAAC,IAAI,CACpB;YACD,KAAK,EAAE,EAAE;SACV,EACD,CAAC,UAAU,EAAE,EAAE,CACb,IAAA,6DAAgC,EAC9B,QAAQ,EACR,cAAc,CAAC,YAAY,EAC3B,UAAU,CACX,EACH,CAAC,UAAU,EAAE,EAAE,CAAC,IAAA,sDAAyB,EAAC,QAAQ,EAAE,UAAU,CAAC,CAChE,CAAC;QAEF,IAAI,oBAAoB,CAAC,IAAI,KAAK,sCAAmB,CAAC,iBAAiB,EAAE;YACvE,OAAO,oBAAoB,CAAC;SAC7B;QAED,OAAO;YACL,IAAI,EAAE,sCAAmB,CAAC,OAAO;YACjC,KAAK,EAAE,IAAA,mEAAsC,EAC3C,cAAc,EACd,oBAAoB,CACrB;SACF,CAAC;IACJ,CAAC;CACF;AA5LD,sCA4LC"}