{"name": "@blue-carbon/backend", "version": "0.1.0", "private": true, "license": "MIT", "scripts": {"start": "node dist/bootstrap.js", "build": "tsc -p tsconfig.build.json", "start:dev": "ts-node-dev --respawn --transpile-only src/bootstrap.ts", "lint": "echo 'lint placeholder'", "test": "echo 'test placeholder'"}, "dependencies": {"@nestjs/apollo": "^12.2.0", "@nestjs/common": "^10.3.7", "@nestjs/core": "^10.3.7", "@nestjs/graphql": "^12.2.0", "@nestjs/platform-express": "^10.3.7", "@nestjs/typeorm": "^11.0.0", "apollo-server-express": "^3.13.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^16.4.5", "graphql": "^16.9.0", "pg": "^8.16.3", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.5", "rxjs": "^7.8.1", "typeorm": "^0.3.27", "web3.storage": "^4.5.5"}, "devDependencies": {"@types/node": "^20.11.30", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.4.5"}}