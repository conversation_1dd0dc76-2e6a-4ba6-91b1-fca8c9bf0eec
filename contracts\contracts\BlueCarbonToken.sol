// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";

interface ICarbonProjectRegistryLike {
    function isVerifier(address account) external view returns (bool);
}

contract BlueCarbonToken is ERC20, Ownable {
    struct ProjectLink { string ipfsHash; address verifier; }

    ICarbonProjectRegistryLike public registry;
    mapping(uint256 => ProjectLink) public projectIdToLink;

    event MintedForProject(uint256 indexed projectId, address indexed to, uint256 amount, address indexed verifier, string ipfsHash);
    event Retired(address indexed account, uint256 indexed projectId, uint256 amount);

    constructor(address owner_, address registry_) ERC20("BlueCarbon", "BCT") Ownable(owner_) {
        registry = ICarbonProjectRegistryLike(registry_);
    }

    function mintForProject(address to, uint256 projectId, uint256 amount, string calldata ipfsHash) external {
        require(registry.isVerifier(msg.sender), "Not verifier");
        _mint(to, amount);
        projectIdToLink[projectId] = ProjectLink({ ipfsHash: ipfsHash, verifier: msg.sender });
        emit MintedForProject(projectId, to, amount, msg.sender, ipfsHash);
    }

    function retire(uint256 amount, uint256 projectId) external {
        _burn(msg.sender, amount);
        emit Retired(msg.sender, projectId, amount);
    }
}
