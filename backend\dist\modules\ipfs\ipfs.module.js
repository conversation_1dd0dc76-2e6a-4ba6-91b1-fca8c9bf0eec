"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpfsModule = exports.IPFS_CLIENT = void 0;
const common_1 = require("@nestjs/common");
const web3_storage_1 = require("web3.storage");
exports.IPFS_CLIENT = 'IPFS_CLIENT';
let IpfsModule = class IpfsModule {
};
exports.IpfsModule = IpfsModule;
exports.IpfsModule = IpfsModule = __decorate([
    (0, common_1.Module)({
        providers: [
            {
                provide: exports.IPFS_CLIENT,
                useFactory: () => new web3_storage_1.Web3Storage({ token: process.env.WEB3_STORAGE_TOKEN || '' }),
            },
        ],
        exports: [exports.IPFS_CLIENT],
    })
], IpfsModule);
//# sourceMappingURL=ipfs.module.js.map