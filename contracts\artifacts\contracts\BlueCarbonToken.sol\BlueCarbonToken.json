{"_format": "hh-sol-artifact-1", "contractName": "BlueCarbonToken", "sourceName": "contracts/BlueCarbonToken.sol", "abi": [{"inputs": [{"internalType": "address", "name": "owner_", "type": "address"}, {"internalType": "address", "name": "registry_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "projectId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "verifier", "type": "address"}, {"indexed": false, "internalType": "string", "name": "ipfsHash", "type": "string"}], "name": "MintedForProject", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "projectId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Retired", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "projectId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "string", "name": "ipfsHash", "type": "string"}], "name": "mintForProject", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "projectIdToLink", "outputs": [{"internalType": "string", "name": "ipfsHash", "type": "string"}, {"internalType": "address", "name": "verifier", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "registry", "outputs": [{"internalType": "contract ICarbonProjectRegistryLike", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "projectId", "type": "uint256"}], "name": "retire", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x60806040523480156200001157600080fd5b506040516200118638038062001186833981016040819052620000349162000168565b816040518060400160405280600a81526020016921363ab2a1b0b93137b760b11b815250604051806040016040528060038152602001621090d560ea1b815250816003908162000085919062000247565b50600462000094828262000247565b5050506001600160a01b038116620000c657604051631e4fbdf760e01b81526000600482015260240160405180910390fd5b620000d181620000f9565b50600680546001600160a01b0319166001600160a01b03929092169190911790555062000313565b600580546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a35050565b80516001600160a01b03811681146200016357600080fd5b919050565b600080604083850312156200017c57600080fd5b62000187836200014b565b915062000197602084016200014b565b90509250929050565b634e487b7160e01b600052604160045260246000fd5b600181811c90821680620001cb57607f821691505b602082108103620001ec57634e487b7160e01b600052602260045260246000fd5b50919050565b601f82111562000242576000816000526020600020601f850160051c810160208610156200021d5750805b601f850160051c820191505b818110156200023e5782815560010162000229565b5050505b505050565b81516001600160401b03811115620002635762000263620001a0565b6200027b81620002748454620001b6565b84620001f2565b602080601f831160018114620002b357600084156200029a5750858301515b600019600386901b1c1916600185901b1785556200023e565b600085815260208120601f198616915b82811015620002e457888601518255948401946001909101908401620002c3565b5085821015620003035787850151600019600388901b60f8161c191681555b5050505050600190811b01905550565b610e6380620003236000396000f3fe608060405234801561001057600080fd5b50600436106101005760003560e01c806370a082311161009757806395d89b411161006657806395d89b4114610230578063a9059cbb14610238578063dd62ed3e1461024b578063f2fde38b1461028457600080fd5b806370a08231146101c3578063715018a6146101ec5780637b103999146101f45780638da5cb5b1461021f57600080fd5b8063293a7f2e116100d3578063293a7f2e1461016b578063307a6aa314610180578063313ce5671461019357806354e86aee146101a257600080fd5b806306fdde0314610105578063095ea7b31461012357806318160ddd1461014657806323b872dd14610158575b600080fd5b61010d610297565b60405161011a9190610a71565b60405180910390f35b610136610131366004610aa7565b610329565b604051901515815260200161011a565b6002545b60405190815260200161011a565b610136610166366004610ad1565b610343565b61017e610179366004610b0d565b610367565b005b61017e61018e366004610b2f565b6103ac565b6040516012815260200161011a565b6101b56101b0366004610bc3565b610531565b60405161011a929190610bdc565b61014a6101d1366004610c06565b6001600160a01b031660009081526020819052604090205490565b61017e6105de565b600654610207906001600160a01b031681565b6040516001600160a01b03909116815260200161011a565b6005546001600160a01b0316610207565b61010d6105f2565b610136610246366004610aa7565b610601565b61014a610259366004610c21565b6001600160a01b03918216600090815260016020908152604080832093909416825291909152205490565b61017e610292366004610c06565b61060f565b6060600380546102a690610c54565b80601f01602080910402602001604051908101604052809291908181526020018280546102d290610c54565b801561031f5780601f106102f45761010080835404028352916020019161031f565b820191906000526020600020905b81548152906001019060200180831161030257829003601f168201915b5050505050905090565b60003361033781858561064d565b60019150505b92915050565b60003361035185828561065f565b61035c8585856106de565b506001949350505050565b610371338361073d565b604051828152819033907fb3775f672e1adf43bf7834b692675bfee4db72f83e42da8cff694ca295a89eca9060200160405180910390a35050565b6006546040516306620a4360e31b81523360048201526001600160a01b0390911690633310521890602401602060405180830381865afa1580156103f4573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906104189190610c8e565b6104585760405162461bcd60e51b815260206004820152600c60248201526b2737ba103b32b934b334b2b960a11b60448201526064015b60405180910390fd5b6104628584610777565b6040805160606020601f85018190040282018101835291810183815290918291908590859081908501838280828437600092018290525093855250503360209384015250868152600790915260409020815181906104c09082610d16565b5060209190910151600190910180546001600160a01b0319166001600160a01b03928316179055604051339187169086907f6541cb037434d6fcc01d6f0a3f0d58eb16739210e033845a5f78f3fae698a37f9061052290889088908890610dd6565b60405180910390a45050505050565b60076020526000908152604090208054819061054c90610c54565b80601f016020809104026020016040519081016040528092919081815260200182805461057890610c54565b80156105c55780601f1061059a576101008083540402835291602001916105c5565b820191906000526020600020905b8154815290600101906020018083116105a857829003601f168201915b505050600190930154919250506001600160a01b031682565b6105e66107ad565b6105f060006107da565b565b6060600480546102a690610c54565b6000336103378185856106de565b6106176107ad565b6001600160a01b03811661064157604051631e4fbdf760e01b81526000600482015260240161044f565b61064a816107da565b50565b61065a838383600161082c565b505050565b6001600160a01b038381166000908152600160209081526040808320938616835292905220546000198110156106d857818110156106c957604051637dc7a0d960e11b81526001600160a01b0384166004820152602481018290526044810183905260640161044f565b6106d88484848403600061082c565b50505050565b6001600160a01b03831661070857604051634b637e8f60e11b81526000600482015260240161044f565b6001600160a01b0382166107325760405163ec442f0560e01b81526000600482015260240161044f565b61065a838383610901565b6001600160a01b03821661076757604051634b637e8f60e11b81526000600482015260240161044f565b61077382600083610901565b5050565b6001600160a01b0382166107a15760405163ec442f0560e01b81526000600482015260240161044f565b61077360008383610901565b6005546001600160a01b031633146105f05760405163118cdaa760e01b815233600482015260240161044f565b600580546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a35050565b6001600160a01b0384166108565760405163e602df0560e01b81526000600482015260240161044f565b6001600160a01b03831661088057604051634a1406b160e11b81526000600482015260240161044f565b6001600160a01b03808516600090815260016020908152604080832093871683529290522082905580156106d857826001600160a01b0316846001600160a01b03167f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925846040516108f391815260200190565b60405180910390a350505050565b6001600160a01b03831661092c5780600260008282546109219190610e0c565b9091555061099e9050565b6001600160a01b0383166000908152602081905260409020548181101561097f5760405163391434e360e21b81526001600160a01b0385166004820152602481018290526044810183905260640161044f565b6001600160a01b03841660009081526020819052604090209082900390555b6001600160a01b0382166109ba576002805482900390556109d9565b6001600160a01b03821660009081526020819052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef83604051610a1e91815260200190565b60405180910390a3505050565b6000815180845260005b81811015610a5157602081850181015186830182015201610a35565b506000602082860101526020601f19601f83011685010191505092915050565b602081526000610a846020830184610a2b565b9392505050565b80356001600160a01b0381168114610aa257600080fd5b919050565b60008060408385031215610aba57600080fd5b610ac383610a8b565b946020939093013593505050565b600080600060608486031215610ae657600080fd5b610aef84610a8b565b9250610afd60208501610a8b565b9150604084013590509250925092565b60008060408385031215610b2057600080fd5b50508035926020909101359150565b600080600080600060808688031215610b4757600080fd5b610b5086610a8b565b94506020860135935060408601359250606086013567ffffffffffffffff80821115610b7b57600080fd5b818801915088601f830112610b8f57600080fd5b813581811115610b9e57600080fd5b896020828501011115610bb057600080fd5b9699959850939650602001949392505050565b600060208284031215610bd557600080fd5b5035919050565b604081526000610bef6040830185610a2b565b905060018060a01b03831660208301529392505050565b600060208284031215610c1857600080fd5b610a8482610a8b565b60008060408385031215610c3457600080fd5b610c3d83610a8b565b9150610c4b60208401610a8b565b90509250929050565b600181811c90821680610c6857607f821691505b602082108103610c8857634e487b7160e01b600052602260045260246000fd5b50919050565b600060208284031215610ca057600080fd5b81518015158114610a8457600080fd5b634e487b7160e01b600052604160045260246000fd5b601f82111561065a576000816000526020600020601f850160051c81016020861015610cef5750805b601f850160051c820191505b81811015610d0e57828155600101610cfb565b505050505050565b815167ffffffffffffffff811115610d3057610d30610cb0565b610d4481610d3e8454610c54565b84610cc6565b602080601f831160018114610d795760008415610d615750858301515b600019600386901b1c1916600185901b178555610d0e565b600085815260208120601f198616915b82811015610da857888601518255948401946001909101908401610d89565b5085821015610dc65787850151600019600388901b60f8161c191681555b5050505050600190811b01905550565b83815260406020820152816040820152818360608301376000818301606090810191909152601f909201601f1916010192915050565b8082018082111561033d57634e487b7160e01b600052601160045260246000fdfea26469706673582212201d1a02a89413d48a5a0f251f162a1d8e13475d1beb812e345e3f772680a03d8664736f6c63430008180033", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106101005760003560e01c806370a082311161009757806395d89b411161006657806395d89b4114610230578063a9059cbb14610238578063dd62ed3e1461024b578063f2fde38b1461028457600080fd5b806370a08231146101c3578063715018a6146101ec5780637b103999146101f45780638da5cb5b1461021f57600080fd5b8063293a7f2e116100d3578063293a7f2e1461016b578063307a6aa314610180578063313ce5671461019357806354e86aee146101a257600080fd5b806306fdde0314610105578063095ea7b31461012357806318160ddd1461014657806323b872dd14610158575b600080fd5b61010d610297565b60405161011a9190610a71565b60405180910390f35b610136610131366004610aa7565b610329565b604051901515815260200161011a565b6002545b60405190815260200161011a565b610136610166366004610ad1565b610343565b61017e610179366004610b0d565b610367565b005b61017e61018e366004610b2f565b6103ac565b6040516012815260200161011a565b6101b56101b0366004610bc3565b610531565b60405161011a929190610bdc565b61014a6101d1366004610c06565b6001600160a01b031660009081526020819052604090205490565b61017e6105de565b600654610207906001600160a01b031681565b6040516001600160a01b03909116815260200161011a565b6005546001600160a01b0316610207565b61010d6105f2565b610136610246366004610aa7565b610601565b61014a610259366004610c21565b6001600160a01b03918216600090815260016020908152604080832093909416825291909152205490565b61017e610292366004610c06565b61060f565b6060600380546102a690610c54565b80601f01602080910402602001604051908101604052809291908181526020018280546102d290610c54565b801561031f5780601f106102f45761010080835404028352916020019161031f565b820191906000526020600020905b81548152906001019060200180831161030257829003601f168201915b5050505050905090565b60003361033781858561064d565b60019150505b92915050565b60003361035185828561065f565b61035c8585856106de565b506001949350505050565b610371338361073d565b604051828152819033907fb3775f672e1adf43bf7834b692675bfee4db72f83e42da8cff694ca295a89eca9060200160405180910390a35050565b6006546040516306620a4360e31b81523360048201526001600160a01b0390911690633310521890602401602060405180830381865afa1580156103f4573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906104189190610c8e565b6104585760405162461bcd60e51b815260206004820152600c60248201526b2737ba103b32b934b334b2b960a11b60448201526064015b60405180910390fd5b6104628584610777565b6040805160606020601f85018190040282018101835291810183815290918291908590859081908501838280828437600092018290525093855250503360209384015250868152600790915260409020815181906104c09082610d16565b5060209190910151600190910180546001600160a01b0319166001600160a01b03928316179055604051339187169086907f6541cb037434d6fcc01d6f0a3f0d58eb16739210e033845a5f78f3fae698a37f9061052290889088908890610dd6565b60405180910390a45050505050565b60076020526000908152604090208054819061054c90610c54565b80601f016020809104026020016040519081016040528092919081815260200182805461057890610c54565b80156105c55780601f1061059a576101008083540402835291602001916105c5565b820191906000526020600020905b8154815290600101906020018083116105a857829003601f168201915b505050600190930154919250506001600160a01b031682565b6105e66107ad565b6105f060006107da565b565b6060600480546102a690610c54565b6000336103378185856106de565b6106176107ad565b6001600160a01b03811661064157604051631e4fbdf760e01b81526000600482015260240161044f565b61064a816107da565b50565b61065a838383600161082c565b505050565b6001600160a01b038381166000908152600160209081526040808320938616835292905220546000198110156106d857818110156106c957604051637dc7a0d960e11b81526001600160a01b0384166004820152602481018290526044810183905260640161044f565b6106d88484848403600061082c565b50505050565b6001600160a01b03831661070857604051634b637e8f60e11b81526000600482015260240161044f565b6001600160a01b0382166107325760405163ec442f0560e01b81526000600482015260240161044f565b61065a838383610901565b6001600160a01b03821661076757604051634b637e8f60e11b81526000600482015260240161044f565b61077382600083610901565b5050565b6001600160a01b0382166107a15760405163ec442f0560e01b81526000600482015260240161044f565b61077360008383610901565b6005546001600160a01b031633146105f05760405163118cdaa760e01b815233600482015260240161044f565b600580546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a35050565b6001600160a01b0384166108565760405163e602df0560e01b81526000600482015260240161044f565b6001600160a01b03831661088057604051634a1406b160e11b81526000600482015260240161044f565b6001600160a01b03808516600090815260016020908152604080832093871683529290522082905580156106d857826001600160a01b0316846001600160a01b03167f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925846040516108f391815260200190565b60405180910390a350505050565b6001600160a01b03831661092c5780600260008282546109219190610e0c565b9091555061099e9050565b6001600160a01b0383166000908152602081905260409020548181101561097f5760405163391434e360e21b81526001600160a01b0385166004820152602481018290526044810183905260640161044f565b6001600160a01b03841660009081526020819052604090209082900390555b6001600160a01b0382166109ba576002805482900390556109d9565b6001600160a01b03821660009081526020819052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef83604051610a1e91815260200190565b60405180910390a3505050565b6000815180845260005b81811015610a5157602081850181015186830182015201610a35565b506000602082860101526020601f19601f83011685010191505092915050565b602081526000610a846020830184610a2b565b9392505050565b80356001600160a01b0381168114610aa257600080fd5b919050565b60008060408385031215610aba57600080fd5b610ac383610a8b565b946020939093013593505050565b600080600060608486031215610ae657600080fd5b610aef84610a8b565b9250610afd60208501610a8b565b9150604084013590509250925092565b60008060408385031215610b2057600080fd5b50508035926020909101359150565b600080600080600060808688031215610b4757600080fd5b610b5086610a8b565b94506020860135935060408601359250606086013567ffffffffffffffff80821115610b7b57600080fd5b818801915088601f830112610b8f57600080fd5b813581811115610b9e57600080fd5b896020828501011115610bb057600080fd5b9699959850939650602001949392505050565b600060208284031215610bd557600080fd5b5035919050565b604081526000610bef6040830185610a2b565b905060018060a01b03831660208301529392505050565b600060208284031215610c1857600080fd5b610a8482610a8b565b60008060408385031215610c3457600080fd5b610c3d83610a8b565b9150610c4b60208401610a8b565b90509250929050565b600181811c90821680610c6857607f821691505b602082108103610c8857634e487b7160e01b600052602260045260246000fd5b50919050565b600060208284031215610ca057600080fd5b81518015158114610a8457600080fd5b634e487b7160e01b600052604160045260246000fd5b601f82111561065a576000816000526020600020601f850160051c81016020861015610cef5750805b601f850160051c820191505b81811015610d0e57828155600101610cfb565b505050505050565b815167ffffffffffffffff811115610d3057610d30610cb0565b610d4481610d3e8454610c54565b84610cc6565b602080601f831160018114610d795760008415610d615750858301515b600019600386901b1c1916600185901b178555610d0e565b600085815260208120601f198616915b82811015610da857888601518255948401946001909101908401610d89565b5085821015610dc65787850151600019600388901b60f8161c191681555b5050505050600190811b01905550565b83815260406020820152816040820152818360608301376000818301606090810191909152601f909201601f1916010192915050565b8082018082111561033d57634e487b7160e01b600052601160045260246000fdfea26469706673582212201d1a02a89413d48a5a0f251f162a1d8e13475d1beb812e345e3f772680a03d8664736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}