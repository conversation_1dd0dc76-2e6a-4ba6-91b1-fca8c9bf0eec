import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      useFactory: () => {
        // If DATABASE_URL is provided, use Postgres. Otherwise fall back to in-memory sqlite for local dev.
        if (process.env.DATABASE_URL) {
          return {
            type: 'postgres',
            url: process.env.DATABASE_URL,
            autoLoadEntities: true,
            synchronize: true,
            ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false as any,
          };
        }

        // Dev fallback: sqlite in-memory
        return {
          type: 'sqlite',
          database: ':memory:',
          autoLoadEntities: true,
          synchronize: true,
        } as any;
      }
    }),
  ],
})
export class DatabaseModule {}
