import 'reflect-metadata';
import * as dotenv from 'dotenv';

// Early diagnostics to help capture startup failures before app imports run
console.log('--- bootstrap start ---');
console.log('cwd:', process.cwd());
console.log('node version:', process.version);
console.log('PORT env:', process.env.PORT);
console.log('WEB3_STORAGE_TOKEN present ->', !!process.env.WEB3_STORAGE_TOKEN);
console.log('globalThis.crypto exists ->', typeof (globalThis as any).crypto !== 'undefined');

// Polyfill Web Crypto for libraries expecting globalThis.crypto in Node <19
try {
	// eslint-disable-next-line @typescript-eslint/no-var-requires
	(globalThis as any).crypto = (globalThis as any).crypto || require('crypto').webcrypto;
} catch (e) {
	console.error('crypto polyfill failed:', e && e.stack ? e.stack : e);
}

dotenv.config();

// Load the actual application entrypoint
require('./main');
