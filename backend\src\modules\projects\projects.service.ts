import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProjectEntity } from './project.entity';

@Injectable()
export class ProjectsService {
  constructor(@InjectRepository(ProjectEntity) private readonly repo: Repository<ProjectEntity>) {}

  async list(): Promise<ProjectEntity[]> {
    return this.repo.find({ order: { id: 'ASC' } });
  }

  async register(input: { name: string; geojson: string; ipfsHash: string }): Promise<ProjectEntity> {
    const p = this.repo.create({ name: input.name, geojson: input.geojson, ipfsHash: input.ipfsHash, verified: false });
    return this.repo.save(p);
  }
}
