import { Resolver, Query, Mutation, Args, ObjectType, Field, ID } from '@nestjs/graphql';
import { ProjectsService } from './projects.service';

@ObjectType()
class Project {
  @Field(() => ID)
  id!: string;

  @Field()
  name!: string;

  @Field()
  geojson!: string;

  @Field({ nullable: true })
  ipfsHash!: string | null;

  @Field()
  verified!: boolean;
}

@Resolver(() => Project)
export class ProjectsResolver {
  constructor(private readonly svc: ProjectsService) {}

  @Query(() => [Project])
  async projects(): Promise<Project[]> {
    return (await this.svc.list()) as unknown as Project[];
  }

  @Mutation(() => Project)
  async registerProject(
    @Args('name') name: string,
    @Args('geojson') geojson: string,
    @Args('ipfsHash') ipfsHash: string,
  ): Promise<Project> {
    return (await this.svc.register({ name, geojson, ipfsHash })) as unknown as Project;
  }
}
