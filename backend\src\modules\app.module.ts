import { Modu<PERSON> } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { join } from 'path';
import { HealthController } from './health.controller';
import { ProjectsModule } from './projects/projects.module';
import { DatabaseModule } from './database.module';
import { IpfsModule } from './ipfs/ipfs.module';

@Module({
  imports: [
    DatabaseModule,
    IpfsModule,
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: join(process.cwd(), 'schema.gql'),
      sortSchema: true,
      playground: true,
      introspection: true,
    }),
    ProjectsModule,
  ],
  controllers: [HealthController],
})
export class AppModule {}
