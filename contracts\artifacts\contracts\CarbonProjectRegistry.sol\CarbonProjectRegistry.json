{"_format": "hh-sol-artifact-1", "contractName": "CarbonProjectRegistry", "sourceName": "contracts/CarbonProjectRegistry.sol", "abi": [{"inputs": [{"internalType": "address", "name": "owner_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}], "name": "ProjectRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "verifier", "type": "address"}, {"indexed": false, "internalType": "string", "name": "ipfsHash", "type": "string"}], "name": "ProjectVerified", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "allowed", "type": "bool"}], "name": "VerifierSet", "type": "event"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isVerifier", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "projects", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "g<PERSON><PERSON><PERSON>", "type": "string"}, {"internalType": "string", "name": "ipfsHash", "type": "string"}, {"internalType": "bool", "name": "verified", "type": "bool"}, {"internalType": "address", "name": "verifier", "type": "address"}, {"internalType": "string", "name": "verificationMetadata", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "g<PERSON><PERSON><PERSON>", "type": "string"}, {"internalType": "string", "name": "ipfsHash", "type": "string"}], "name": "registerProject", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bool", "name": "allowed", "type": "bool"}], "name": "setVerifier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "verificationMetadata", "type": "string"}, {"internalType": "string", "name": "ipfsHash", "type": "string"}], "name": "verifyProject", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}