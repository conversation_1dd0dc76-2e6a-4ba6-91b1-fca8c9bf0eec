// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";

contract CarbonProjectRegistry is Ownable {
    struct Project {
        uint256 id;
        address creator;
        string name;
        string geojson; // polygon GeoJSON string
        string ipfsHash; // metadata/documents hash
        bool verified;
        address verifier;
        string verificationMetadata; // JSON string
    }

    uint256 public nextId = 1;
    mapping(uint256 => Project) public projects;
    mapping(address => bool) public isVerifier;

    event VerifierSet(address indexed account, bool allowed);
    event ProjectRegistered(uint256 indexed id, address indexed creator, string name);
    event ProjectVerified(uint256 indexed id, address indexed verifier, string ipfsHash);

    constructor(address owner_) Ownable(owner_) {}

    function setVerifier(address account, bool allowed) external onlyOwner {
        isVerifier[account] = allowed;
        emit VerifierSet(account, allowed);
    }

    function registerProject(string calldata name, string calldata geojson, string calldata ipfsHash) external returns (uint256) {
        uint256 id = nextId++;
        projects[id] = Project({
            id: id,
            creator: msg.sender,
            name: name,
            geojson: geojson,
            ipfsHash: ipfsHash,
            verified: false,
            verifier: address(0),
            verificationMetadata: ""
        });
        emit ProjectRegistered(id, msg.sender, name);
        return id;
    }

    function verifyProject(uint256 id, string calldata verificationMetadata, string calldata ipfsHash) external {
        require(isVerifier[msg.sender], "Not verifier");
        Project storage p = projects[id];
        p.verified = true;
        p.verifier = msg.sender;
        p.verificationMetadata = verificationMetadata;
        p.ipfsHash = ipfsHash; // update to verified data hash
        emit ProjectVerified(id, msg.sender, ipfsHash);
    }
}
